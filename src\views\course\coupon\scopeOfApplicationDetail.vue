<script setup>
import { ref, reactive, onMounted, nextTick } from "vue";
import { useRouter, useRoute } from "vue-router";
import { PlusSearch, PlusTable } from "plus-pro-components";
import { ArrowLeft } from "@element-plus/icons-vue";
import { getCouponCourseList } from "@/api/coupon.js";

defineOptions({
  name: "ScopeOfApplicationDetail"
});

const router = useRouter();
const route = useRoute();

// 计算表格高度
const searchFormHeight = ref(0);
const tableHeight = ref("calc(100vh - 350px)");

const calculateTableHeight = async () => {
  await nextTick();
  const searchForm = document.querySelector(".con-content");
  if (searchForm) {
    searchFormHeight.value = searchForm.offsetHeight;
    // 动态计算表格高度，减去搜索框高度、表头按钮高度、分页器高度和其他间距
    tableHeight.value = `calc(100vh - 240px - ${searchFormHeight.value}px)`;
  }
};

// 搜索表单数据
const searchForm = reactive({
  courseName: "",
  coursePeriodName: "",
  coursePeriodStatus: "不限"
});

// 搜索列配置
const searchColumns = ref([
  {
    label: "课程名称",
    prop: "courseName",
    type: "input",
    placeholder: "请输入课程名称"
  },
  {
    label: "课期名称",
    prop: "coursePeriodName",
    type: "input",
    placeholder: "请输入课期名称"
  },
  {
    label: "课期状态",
    prop: "coursePeriodStatus",
    type: "select",
    options: [
      { label: "不限", value: "不限" },
      { label: "已上架", value: "已上架" },
      { label: "未上架", value: "未上架" }
    ]
  }
]);

// 表格列配置
const tableColumns = ref([
  {
    label: "课程名称",
    prop: "courseName",
    minWidth: 200
  },
  {
    label: "课期名称",
    prop: "coursePeriodName",
    minWidth: 150
  },
  {
    label: "期号",
    prop: "issueNumber",
    minWidth: 80,
    align: "center"
  },
  {
    label: "开课时间",
    prop: "startDate",
    minWidth: 120,
    align: "center"
  },
  {
    label: "购买类型",
    prop: "purchaseType",
    minWidth: 100,
    align: "center"
  },
  {
    label: "课期状态",
    prop: "coursePeriodStatus",
    minWidth: 100,
    align: "center",
    formatter: ({ coursePeriodStatus }) => {
      if (coursePeriodStatus === "已上架") {
        return '<span style="color: #67c23a;">已上架</span>';
      } else if (coursePeriodStatus === "未上架") {
        return '<span style="color: #909399;">未上架</span>';
      }
      return coursePeriodStatus;
    }
  }
]);

// 假数据 - 优惠券使用范围列表
const tableData = ref([
  {
    id: 1,
    courseName: "劳动教育系列课程",
    coursePeriodName: "控端课",
    issueNumber: 1,
    startDate: "2025-04-11",
    purchaseType: "普通单",
    coursePeriodStatus: "已上架"
  },
  {
    id: 2,
    courseName: "劳动教育系列课程",
    coursePeriodName: "挖土豆",
    issueNumber: 2,
    startDate: "2025-04-11",
    purchaseType: "普通单",
    coursePeriodStatus: "已上架"
  },
  {
    id: 3,
    courseName: "劳动教育系列课程",
    coursePeriodName: "认识多季稻",
    issueNumber: 3,
    startDate: "2025-04-11",
    purchaseType: "普通单",
    coursePeriodStatus: "已上架"
  },
  {
    id: 4,
    courseName: "劳动教育系列课程",
    coursePeriodName: "快乐一起营",
    issueNumber: 4,
    startDate: "2025-04-11",
    purchaseType: "普通单",
    coursePeriodStatus: "未上架"
  },
  {
    id: 5,
    courseName: "科学教育系列课程",
    coursePeriodName: "我们一起飞",
    issueNumber: 5,
    startDate: "2025-04-11",
    purchaseType: "普通单",
    coursePeriodStatus: "未上架"
  }
]);

// 分页配置
const pagination = reactive({
  page: 1,
  size: 5,
  total: 25
});

// 加载状态
const loading = ref(false);

// 搜索方法
const onSearch = () => {
  loading.value = true;
  // 模拟搜索延迟
  setTimeout(() => {
    // 这里可以根据搜索条件过滤数据
    console.log("搜索条件:", searchForm);
    loading.value = false;
  }, 500);
};

// 重置方法
const onReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = key === "coursePeriodStatus" ? "不限" : "";
  });
  onSearch();
};

// 分页改变
const onPageChange = page => {
  pagination.page = page;
  onSearch();
};

// 每页条数改变
const onSizeChange = size => {
  pagination.size = size;
  pagination.page = 1;
  onSearch();
};

// 返回上一页
const goBack = () => {
  router.go(-1);
};

onMounted(() => {
  // 页面加载时的初始化逻辑
  console.log("优惠券使用范围详情页面加载完成");
  calculateTableHeight();
  window.addEventListener("resize", calculateTableHeight);
});
</script>

<template>
  <div class="page-container">
    <div class="containers">
      <!-- 搜索区域 -->
      <div class="con_search">
        <PlusSearch
          v-model="searchForm"
          :columns="searchColumns"
          :show-number="3"
          :label-width="80"
          :hasUnfold="false"
          :searchOnChange="false"
          @search="onSearch"
          @reset="onReset"
        />
      </div>

      <div class="con-content" :max-height="tableHeight">
        <!-- 数据表格 -->
        <div class="table-container">
          <!-- <div class="table-title">优惠券使用范围</div> -->
          <PlusTable
            v-loading="loading"
            :data="tableData"
            :columns="tableColumns"
            :title-bar="false"
            :border="false"
            class="no-border-table"
          />
        </div>

        <!-- 分页 -->
        <div class="pagination">
          <el-pagination
            background
            layout="total, sizes, prev, pager, next, jumper"
            :current-page="pagination.page"
            :page-size="pagination.size"
            :total="pagination.total"
            :page-sizes="[5, 10, 15, 20]"
            @size-change="onSizeChange"
            @current-change="onPageChange"
          />
        </div>
      </div>

      <!-- 返回按钮 -->
      <div class="action-buttons">
        <el-button @click="goBack"> 返回 </el-button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.breadcrumb {
  padding: 16px 20px;
  background: #fff;
  border-radius: 4px;
  margin-bottom: 16px;
}

.con_search {
  padding: 18px 22px;
  background: #fff;
  border-radius: 4px;
  margin-bottom: 10px;

  /* 隐藏搜索和重置按钮上的图标 */
  :deep(.el-form-item__content .el-button .el-icon) {
    display: none;
    width: 0;
    height: 0;
  }

  :deep(.el-form-item__content) {
    .el-button {
      span {
        margin-left: 0px !important;
      }
    }
  }
}

.table-container {
  background: #ffffff;
  border-radius: 4px;
  margin-bottom: 16px;
  padding: 20px;
  height: 600px;
}

.pagination {
  display: flex;
  justify-content: flex-end;
  padding: 12px 20px;
  background-color: #fff;
  border-radius: 4px;
  margin-bottom: 16px;
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  padding: 0 20px 10px 0px;
  background-color: #fff;
}

/* 去掉表格边框 */
:deep(.el-table) {
  border: none;
}

:deep(.el-table__header th) {
  background-color: #fafafa !important;
  color: #606266;
  font-weight: 600;
}

:deep(.el-table__row) {
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-table__row:hover) {
  background-color: #f5f7fa;
}

/* 表单项样式优化 */
:deep(.el-form-item) {
  margin-bottom: 16px;
}

:deep(.el-col) {
  max-width: 330px !important;
}

/* 搜索按钮样式 */
:deep(.el-button--primary) {
  background-color: #409eff;
  border-color: #409eff;
}

:deep(.el-button--primary:hover) {
  background-color: #66b1ff;
  border-color: #66b1ff;
}

/* 分页样式 */
:deep(.el-pagination.is-background .el-pager li:not(.is-disabled).is-active) {
  background-color: #409eff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .containers {
    padding: 10px;
  }

  .con_search {
    padding: 15px;
  }

  .table-container {
    padding: 15px;
  }
}
.con-content {
  background-color: #fff;
  height: 680px;
}
</style>
