import { ref, reactive, computed, nextTick, watch, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import { debounce, removeEmptyValues } from "@iceywu/utils";
import { addCoupon, getCouponCourse, getCouponFindById } from "@/api/coupon.js";
import { formatTime } from "@/utils/index";
// import { removeEmptyValues } from "@iceywu/utils";
// import { ElMessage } from "element-plus";

export default function useAddCoupon() {
  const router = useRouter();
  const route = useRoute();

  // 表单数据 - 根据API文档字段映射
  const form = reactive({
    name: "", // 优惠券名称
    feeType: "CLASS_HOUR", // 费用类型: CLASS_HOUR,INSURANCE,MATERIAL,SERVICE - 默认选择课时
    couponDiscountType: "FULL_REDUCTION", // 优惠类型: 默认满减券
    totalIssue: "", // 总发行量 - 改为字符串类型，避免数字类型问题
    distributionStartTime: null, // 发放开始时间
    distributionEndTime: null, // 发放结束时间
    startTime: null, // 使用开始时间
    endTime: null, // 使用结束时间
    remarks: "", // 备注 - 改为空字符串
    enabled: true, // 启用状态
    coursePeriodIds: [], // 可使用课期ID数组
    discountAmount: "", // 优惠值 - 改为字符串类型
    conditionAmount: "", // 满减条件金额 - 改为字符串类型
    isUseLimit: false, // 使用是否有限制 - 默认不限制
    noLimitNumber: "", // 每人限领数量 - 改为字符串类型
    couponScope: "ALL", // 优惠券使用范围: ALL(通用),LIMIT(指定)
    usedCourse: "ALL", // 前端使用范围标识 - 默认设置为'ALL'(通用)
    maxAmount: "", // 满金额(前端显示用)
    minAmount: "", // 减金额(前端显示用)

    // 新增字段用于处理使用时间逻辑
    useTimeType: 1, // 使用时间类型: 1-不限, 2-有限
    useTimeRange: [], // 使用时间范围(当useTimeType=2时使用)
    distributionTime: [] // 发放时间范围
  });

  const formRef = ref(null);
  const richFlag = ref(false);

  // 表单验证规则
  const rules = {
    name: [
      { required: true, message: "请输入优惠券名称", trigger: "blur" },
      {
        min: 1,
        max: 10,
        message: "优惠券名称长度在 1 到 10 个字符",
        trigger: "blur"
      }
    ],
    couponDiscountType: [
      { required: true, message: "请选择优惠类型", trigger: "change" }
    ],
    totalIssue: [
      { required: true, message: "请输入总发行量", trigger: "blur" },
      {
        validator: (rule, value, callback) => {
          if (!value || value.trim() === "") {
            callback(new Error("请输入总发行量"));
          } else if (isNaN(parseInt(value)) || parseInt(value) <= 0) {
            callback(new Error("请输入有效的总发行量"));
          } else {
            callback();
          }
        },
        trigger: "blur"
      }
    ],
    distributionTime: [
      {
        required: true,
        validator: (rule, value, callback) => {
          if (!value || !Array.isArray(value) || value.length !== 2) {
            callback(new Error("请选择发放时间"));
          } else {
            callback();
          }
        },
        trigger: "change"
      }
    ],
    useTimeRange: [
      {
        validator: (rule, value, callback) => {
          if (form.useTimeType === 2) {
            if (!value || !Array.isArray(value) || value.length !== 2) {
              callback(new Error("请选择使用时间范围"));
            } else {
              // 检查使用时间是否早于发放时间
              if (form.distributionTime && form.distributionTime.length === 2) {
                const distributionStartTime = new Date(
                  form.distributionTime[0]
                ).getTime();
                const useStartTime = new Date(value[0]).getTime();

                if (useStartTime < distributionStartTime) {
                  callback(new Error("使用时间不能早于发放时间"));
                } else {
                  callback();
                }
              } else {
                callback();
              }
            }
          } else {
            callback();
          }
        },
        trigger: "change"
      }
    ],
    conditionAmount: [
      {
        validator: (rule, value, callback) => {
          if (form.couponDiscountType === "FULL_REDUCTION") {
            if (!value) {
              callback(new Error("请输入满减条件金额"));
            } else if (isNaN(parseFloat(value)) || parseFloat(value) <= 0) {
              callback(new Error("请输入有效的满减条件金额"));
            } else {
              // 检查条件金额是否小于减免金额
              if (
                form.discountAmount &&
                parseFloat(value) < parseFloat(form.discountAmount)
              ) {
                callback(new Error("条件金额不能小于减免金额"));
              } else {
                callback();
              }
            }
          } else {
            callback();
          }
        },
        trigger: "blur"
      }
    ],
    discountAmount: [
      {
        validator: (rule, value, callback) => {
          console.log("🌳-----value-----", value);
          if (form.couponDiscountType === "FULL_REDUCTION") {
            if (!value) {
              callback(new Error("请输入优惠值"));
            } else if (isNaN(parseFloat(value)) || parseFloat(value) <= 0) {
              callback(new Error("请输入有效的优惠值"));
            } else {
              // 检查减免金额是否大于条件金额
              if (
                form.conditionAmount &&
                parseFloat(value) > parseFloat(form.conditionAmount)
              ) {
                callback(new Error("减免金额不能大于条件金额"));
              } else {
                callback();
              }
            }
          } else {
            callback();
          }
        },
        trigger: "blur"
      }
    ],
    noLimitNumber: [
      {
        validator: (rule, value, callback) => {
          if (value && value.trim() !== "") {
            if (isNaN(parseInt(value)) || parseInt(value) <= 0) {
              callback(new Error("请输入有效的每人限领数量"));
            } else {
              callback();
            }
          } else {
            callback();
          }
        },
        trigger: "blur"
      }
    ]
  };

  // 监听使用时间类型变化，自动清空时间范围
  watch(
    () => form.useTimeType,
    (newValue, oldValue) => {
      if (newValue === 1) {
        // 选择"不限"时，清空时间范围
        form.useTimeRange = [];
        form.startTime = null;
        form.endTime = null;
      }
    }
  );

  // 表单文件数据
  const formFile = ref({
    institutionLicense: [],
    qualificationDocuments: [],
    logo: [],
    video: [],
    environment: []
  });

  // 基本信息表单配置 - 根据API文档字段映射
  const formData = ref([
    {
      label: "优惠券名称",
      type: "input",
      prop: "name",
      check: true,
      width: "400px",
      placeholder: "请输入优惠券名称"
    },
    {
      label: "优惠券费用类型",
      type: "radio",
      check: true,
      width: "400px",
      prop: "feeType",
      placeholder: "请选择费用类型",
      options: [
        { name: "课时", value: "CLASS_HOUR" },
        { name: "材料", value: "MATERIAL" }
      ]
    },
    {
      label: "优惠券类型",
      type: "radio",
      check: true,
      width: "400px",
      prop: "couponDiscountType",
      placeholder: "请选择优惠类型",
      options: [{ name: "满减券", value: "FULL_REDUCTION" }]
    },
    {
      label: "",
      type: "radioInput",
      // check: true,
      prop: "fullReduction",
      maxLength: 30,
      width: "400px",
      placeholder: "满减条件设置",
      // 只有当选择满减券时才显示
      show: () => form.couponDiscountType === "FULL_REDUCTION"
    },
    {
      label: "发行数量",
      type: "input",
      typeInput: "number",
      check: true,
      maxLength: 11,
      width: "400px",
      prop: "totalIssue",
      placeholder: "请输入发行数量"
    },
    {
      label: "发放时间",
      type: "date",
      check: true,
      prop: "distributionTime",
      maxLength: 20,
      width: "400px",
      placeholder: "请选择发放时间"
    },
    {
      label: "使用时间限制",
      type: "radio",
      check: true,
      prop: "useTimeType",
      maxLength: 20,
      placeholder: "请选择使用时间限制",
      options: [
        { name: "不限", value: 1 },
        { name: "有限", value: 2 }
      ]
    },
    {
      label: "",
      type: "date",
      check: false,
      prop: "useTimeRange",
      width: "400px",
      placeholder: "请选择使用时间范围",
      show: () => form.useTimeType === 2
    },

    {
      label: "备注",
      type: "textarea",
      prop: "remarks",
      width: "400px",
      maxLength: 200,
      placeholder: "请输入备注"
    },
    {
      label: "状态",
      type: "radio",
      check: true,
      prop: "enabled",
      maxLength: 50,
      placeholder: "请选择状态",
      options: [
        { name: "启用", value: true },
        { name: "停用", value: false }
      ]
    }
  ]);

  // 使用范围表单配置
  const formData2 = ref([
    {
      label: "使用范围",
      type: "radio",
      check: true,
      maxLength: 50,
      width: "400px",
      prop: "usedCourse",
      placeholder: "请选择使用范围",
      options: [
        { name: "通用", value: "ALL" },
        { name: "指定", value: "LIMIT" }
      ]
    }
  ]);

  // 优惠券使用范围相关数据 - 根据API文档参数
  const scopeForm = reactive({
    courseName: "",
    coursePeriodName: "",
    startTime: "",
    endTime: "",
    coursePeriodState: ""
  });

  // 搜索列配置 - 根据API文档参数
  const searchColumns = ref([
    {
      label: "课程名称",
      prop: "courseName",
      type: "input",
      placeholder: "请输入课程名称",
      span: 6
    },
    {
      label: "课期名称",
      prop: "coursePeriodName",
      type: "input",
      placeholder: "请输入课期名称",
      span: 6
    }
  ]);

  // 表格列配置 - 根据API返回数据结构
  const tableColumns = ref([
    {
      label: "",
      type: "selection",
      width: 55,
      align: "center"
    },
    {
      label: "课程名称",
      prop: "courseName",
      minWidth: 150
    },
    {
      label: "课期名称",
      prop: "coursePeriodName",
      minWidth: 200
    },
    {
      label: "期号",
      prop: "termNumber",
      width: 120,
      align: "center"
    },
    {
      label: "开课时间",
      prop: "openTime",
      width: 120,
      align: "center",
      render: (cellValue, row) => {
        return formatTime(row.row.openTime, "YYYY-MM-DD HH:mm");
      }
    },
    {
      label: "购买类型",
      prop: "buyType",
      width: 120,
      align: "center",
      render: (cellValue, row) => {
        const buyType = row.row.buyType;
        // 购买类型映射
        const buyTypeMap = {
          ORDINARY: "普通单",
          PRIVATE_DOMAIN_GROUP_ORDER: "团购单"
        };
        return buyTypeMap[buyType] || buyType || "-";
      }
    },
    {
      label: "课程状态",
      prop: "coursePeriodState",
      width: 100,
      align: "center",
      render: (cellValue, row) => {
        const state = row.row.coursePeriodState;
        // 课期状态映射
        const stateMap = {
          OFFLINE: "下线",
          OFFLINE_UNDER_REVIEW: "下线审核中",
          ONLINE: "上线",
          ONLINE_UNDER_REVIEW: "上线审核中",
          COMPLETED: "已完成",
          NOT_LISTED: "未上架"
        };

        // 根据状态返回不同颜色的文本
        const stateText = stateMap[state] || state || "-";

        return stateText;
      }
    }
  ]);

  // 初始数据 - 课程列表
  const tableData = ref([]);

  // 确保表格数据的稳定性
  const stableTableData = computed(() => {
    return tableData.value.map(item => {
      return {
        courseName: item.course.name,
        coursePeriodName: item.name,
        termNumber: item.termNumber,
        openTime: item.openTime,
        buyType: item.buyType,
        coursePeriodState: item.coursePeriodState
      };
    });
  });

  // 分页配置 - 根据API文档，page从0开始
  const pagination = reactive({
    page: 0,
    size: 10,
    total: 0
  });

  // 加载状态
  const loading = ref(false);
  const getListLoading = ref(false);

  // 选中的行
  const selectedRows = ref([]);

  // 已选中的行 ID（初始回显数据）
  const selectedRowKeys = ref([]);

  // 存储选中行的详细信息
  const rowList = ref({
    coursePeriodId: null,
    couponAvailable: null,
    couponIds: []
  });

  // 计算属性：是否显示使用范围选择区域
  const showScopeSelection = computed(() => {
    console.log("🍪-----form.usedCourse-----", form.usedCourse);
    console.log("🍪-----showScopeSelection-----", form.usedCourse === "LIMIT");
    return form.usedCourse === "LIMIT"; // 当选择"指定"时显示
  });

  // 搜索方法
  const onSearch = async () => {
    if (loading.value) return; // 防止重复搜索

    // 通用模式下不需要搜索课程
    if (form.usedCourse === "ALL") {
      loading.value = false;
      return;
    }

    loading.value = true;
    try {
      // 构建查询参数
      const params = {
        page: pagination.page,
        size: Number(pagination.size),
        sort: "createdAt,desc",
        courseName: scopeForm.courseName || undefined,
        coursePeriodName: scopeForm.coursePeriodName || undefined
      };

      // 移除undefined的参数
      Object.keys(params).forEach(key => {
        if (params[key] === undefined) {
          delete params[key];
        }
      });

      // 调用API
      const result = await getCouponCourse(params);

      if (result.code === 200) {
        // tableData.value = result.data.content || [];
        pagination.total = result.data.totalElements || 0;
        pagination.page = result.data.number || 0;
        pagination.size = result.data.size || 20;
        tableData.value = result.data?.content?.map(item => {
          return {
            id: item.id,
            courseName: item.course?.name || "-",
            coursePeriodName: item?.name || "-",
            termNumber: item?.termNumber || "-",
            openTime: item?.openTime || "-",
            buyType: item?.buyType || "-",
            coursePeriodState: item?.coursePeriodState || "-"
          };
        });
      } else {
        ElMessage.error(result.msg || "查询失败");
      }
    } catch (error) {
      ElMessage.error("查询课程失败");
    } finally {
      loading.value = false;
    }
  };

  // 重置方法
  const onReset = () => {
    Object.keys(scopeForm).forEach(key => {
      scopeForm[key] = "";
    });
    pagination.page = 0;
    onSearch();
  };

  // 分页改变 - 根据API文档，page从0开始
  const onPageChange = page => {
    if (pagination.page === page) return; // 防止重复更新

    pagination.page = page - 1; // 转换为API的page格式（从0开始）
    onSearch();
  };

  // 每页条数改变
  const onSizeChange = size => {
    if (pagination.size === size) return; // 防止重复更新

    pagination.size = size;
    pagination.page = 0; // 重置到第一页（API格式）
    onSearch();
  };

  // 选择改变
  const onSelectionChange = selection => {
    // 使用 nextTick 避免递归更新
    nextTick(() => {
      // 防止递归更新，比较数组内容而不是引用
      const isSameSelection =
        JSON.stringify(selectedRows.value.map(row => row.id).sort()) ===
        JSON.stringify(selection.map(row => row.id).sort());

      if (!isSameSelection) {
        selectedRows.value = [...selection]; // 使用展开运算符创建新数组

        // 同步更新selectedRowKeys
        selectedRowKeys.value = selection.map(row => row.id);

        // 更新rowList
        if (selection && selection.length > 0) {
          rowList.value = {
            coursePeriodId: selection[0].id,
            couponAvailable: true,
            couponIds: selection.map(row => row.id)
          };
        } else {
          rowList.value = {
            coursePeriodId: null,
            couponAvailable: null,
            couponIds: []
          };
        }

        console.log("🍪-----选中的行:", selection);
        console.log("🍪-----更新后的selectedRowKeys:", selectedRowKeys.value);
        console.log("🍪-----更新后的rowList:", rowList.value);
      }
    });
  };

  // 监听使用范围变化
  const onScopeChange = value => {
    console.log("🍪-----onScopeChange value-----", value);
    console.log("🍪-----form.usedCourse before-----", form.usedCourse);

    // 使用 nextTick 避免在同一个事件循环中更新
    nextTick(() => {
      if (value === "ALL") {
        // 选择"通用"时，清空选中的课程
        selectedRows.value = [];
        selectedRowKeys.value = [];
        form.couponScope = "ALL";
        form.coursePeriodIds = [];
        // 通用模式下不调用接口，因为所有课程都可以使用
        console.log("🍪-----通用模式：所有课程均可使用，无需查询具体课程");
      } else if (value === "LIMIT") {
        // 选择"指定"时，设置使用范围并加载课程列表
        form.couponScope = "LIMIT";
        // 指定模式下需要加载课程列表供选择
        onSearch();
      }
      console.log("使用范围变化:", value);
      console.log("🍪-----form.usedCourse after-----", form.usedCourse);
      console.log("🍪-----form.couponScope-----", form.couponScope);
    });
  };

  // 返回上一页
  const reset = () => {
    router.go(-1);
  };

  // 处理时间字段转换
  const handleTimeField = timeValue => {
    if (!timeValue) return 0;
    if (Array.isArray(timeValue) && timeValue.length === 2) {
      // 如果是日期范围数组，取开始时间
      return new Date(timeValue[0]).getTime();
    }
    if (typeof timeValue === "string") {
      return new Date(timeValue).getTime();
    }
    return timeValue;
  };

  // 处理发放时间
  const handleDistributionTime = timeValue => {
    if (!timeValue) return { start: 0, end: 0 };
    if (Array.isArray(timeValue) && timeValue.length === 2) {
      return {
        start: new Date(timeValue[0]).getTime(),
        end: new Date(timeValue[1]).getTime()
      };
    }
    return { start: 0, end: 0 };
  };

  // 提交处理
  const onSubmit = async () => {
    if (getListLoading.value) return;

    try {
      // 表单验证
      const valid = await formRef.value.validate();

      if (!valid) {
        return;
      }

      // 验证使用范围
      if (form.usedCourse === "LIMIT" && selectedRows.value.length === 0) {
        ElMessage.warning("请至少选择一个课程");
        return;
      }

      // 构建API请求数据 - 严格按照API文档字段类型
      let paramsData = {
        // 必填字段
        name: form.name || "", // string - 名称
        couponDiscountType: form.couponDiscountType || "", // string - 优惠类型
        totalIssue: parseInt(form.totalIssue) || 0, // integer(int32) - 总发行量
        distributionStartTime: 0, // integer(int64) - 发放开始时间，将在下面处理
        distributionEndTime: 0, // integer(int64) - 结束发放时间，将在下面处理
        isUseLimit: Boolean(form.isUseLimit), // boolean - 使用是否有限制
        couponScope: form.couponScope || "ALL", // string - 优惠券使用范围
        enabled: Boolean(form.enabled), // boolean - 启用状态

        // 可选字段
        feeType: form.feeType || null, // string - 费用类型，可选
        conditionAmount: form.conditionAmount
          ? parseFloat(form.conditionAmount)
          : 0, // number - 满减条件金额
        discountAmount: form.discountAmount
          ? parseFloat(form.discountAmount)
          : 0, // number - 优惠值
        startTime: form.startTime || 0, // integer(int64) - 使用开始时间
        endTime: form.endTime || 0, // integer(int64) - 使用结束时间
        remarks: form?.remarks, // string - 备注
        noLimitNumber: form.noLimitNumber ? parseInt(form.noLimitNumber) : 0, // integer(int32) - 每人限领数量
        coursePeriodIds: Array.isArray(form.coursePeriodIds)
          ? form.coursePeriodIds
          : [] // array - 可使用课期
      };

      // 处理发放时间
      const distributionTime = handleDistributionTime(form.distributionTime);
      paramsData.distributionStartTime = distributionTime.start;
      paramsData.distributionEndTime = distributionTime.end;

      // 确保时间字段为整数类型
      paramsData.distributionStartTime =
        parseInt(paramsData.distributionStartTime) || 0;
      paramsData.distributionEndTime =
        parseInt(paramsData.distributionEndTime) || 0;
      paramsData.startTime = parseInt(paramsData.startTime) || 0;
      paramsData.endTime = parseInt(paramsData.endTime) || 0;

      // 如果选择"指定"使用范围，设置课期ID数组
      if (form.usedCourse === "LIMIT") {
        paramsData.coursePeriodIds = selectedRows.value.map(row => row.id);
      }

      // 处理满减券金额字段
      if (form.couponDiscountType === "FULL_REDUCTION") {
        // 满减券：满金额 -> conditionAmount，减金额 -> discountAmount
        if (form.conditionAmount && parseFloat(form.conditionAmount) > 0) {
          paramsData.conditionAmount = parseFloat(form.conditionAmount);
        }
        if (form.discountAmount && parseFloat(form.discountAmount) > 0) {
          paramsData.discountAmount = parseFloat(form.discountAmount);
        }
      }

      // 调用新增优惠券API
      getListLoading.value = true;
      const result = await addCoupon(paramsData);

      if (result.code === 200) {
        ElMessage.success("优惠券创建成功！");
        router.go(-1);
      } else {
        ElMessage.error(result.message || "创建失败，请重试");
      }
    } catch (error) {
      ElMessage.error("创建失败，请重试");
    } finally {
      getListLoading.value = false;
    }
  };

  // 提交表单
  const submitForm = debounce(
    () => {
      formRef.value.validate(valid => {
        if (valid) {
          onSubmit();
        } else {
        }
      });
    },
    1000,
    { immediate: true }
  );

  // 其他数据
  const newData = ref();
  const oldData = ref();

  // 表格引用
  // const tableRef = ref(null);

  // 加载课期信息并勾选已有项
  const loadCoursePeriodsAndSelectExisting = async existingCoursePeriods => {
    try {
      // 获取所有可用的课期数据
      const params = {
        page: 0,
        size: 1000, // 获取足够多的数据
        sort: "createdAt,desc"
      };

      const result = await getCouponCourse(params);
      if (result.code === 200 && result.data?.content) {
        const allCoursePeriods = result.data.content;

        // 设置表格数据
        tableData.value = allCoursePeriods.map(item => ({
          id: item.id,
          courseName: item.course?.name || "-",
          coursePeriodName: item.name || "-",
          termNumber: item.termNumber || "-",
          openTime: item.openTime || "-",
          buyType: item.buyType || "-",
          coursePeriodState: item.coursePeriodState || "-"
        }));

        // 从existingCoursePeriods中提取ID（接口返回的简化数据结构）
        const existingIds = existingCoursePeriods.map(item => item.id);

        // 找到需要勾选的已有项
        const rowsToSelect = tableData.value.filter(item =>
          existingIds.includes(item.id)
        );

        // 设置选中的行
        selectedRows.value = rowsToSelect;

        // 设置选中的行ID，用于复选框回显
        selectedRowKeys.value = existingIds;

        // 更新rowList
        rowList.value = {
          coursePeriodId: existingIds[0] || null,
          couponAvailable: true,
          couponIds: existingIds
        };

        console.log("🍪-----课期数据加载完成-----", tableData.value);
        console.log("🍪-----选中的行:", selectedRows.value);
        console.log("🍪-----选中的行ID:", selectedRowKeys.value);
        console.log("🍪-----rowList:", rowList.value);

        // 添加调试信息，检查表格状态
        console.log("🍪-----表格数据长度:", tableData.value.length);
        console.log("🍪-----选中行数量:", selectedRows.value.length);
        console.log("🍪-----需要勾选的ID:", existingIds);
        console.log(
          "🍪-----表格中可勾选的ID:",
          tableData.value.map(item => item.id)
        );

        // 验证勾选状态
        const selectedIds = selectedRows.value.map(row => row.id);
        const missingSelections = existingIds.filter(
          id => !selectedIds.includes(id)
        );
        if (missingSelections.length > 0) {
          console.warn("🍪-----以下ID未能勾选:", missingSelections);
        } else {
          console.log("🍪-----所有已有项都已正确勾选");
        }

        // 强制更新表格，确保勾选状态正确显示
        await nextTick();
        console.log("🍪-----表格更新完成，当前选中状态:", selectedRows.value);
        console.log("🍪-----当前selectedRowKeys:", selectedRowKeys.value);

        // 尝试手动设置选中状态
        if (rowsToSelect.length > 0) {
          // 延迟一下再设置，确保表格已经完全渲染
          setTimeout(() => {
            selectedRows.value = [...rowsToSelect];
            console.log("🎁-----selectedRows.value-----", selectedRows.value);

            // // 尝试手动设置表格的选中状态
            // if (tableRef.value && tableRef.value.setSelection) {
            //   try {
            //     rowsToSelect.forEach(row => {
            //       tableRef.value.setSelection(row, true);
            //     });
            //   } catch (error) {
            //     console.warn("🍪-----手动设置表格选中状态失败:", error);
            //   }
            // }
          }, 100);
        }
      }
    } catch (error) {
      console.error("🍪-----加载课期数据失败-----", error);
    }
  };

  // 加载优惠券数据
  const loadCouponData = async id => {
    try {
      loading.value = true;
      const result = await getCouponFindById({ id });

      if (result.code === 200 && result.data) {
        const couponData = result.data;

        try {
          // 回显表单数据
          form.name = couponData.name || "";
          form.feeType = couponData.feeType || "CLASS_HOUR";
          form.couponDiscountType =
            couponData.couponDiscountType || "FULL_REDUCTION";
          form.totalIssue = couponData.totalIssue
            ? String(couponData.totalIssue)
            : "";
          form.remarks = couponData.remarks || "";
          form.enabled =
            couponData.enabled !== undefined ? couponData.enabled : true;
          form.couponScope = couponData.couponScope || "ALL";
          form.usedCourse = couponData.couponScope || "ALL"; // 同步使用范围

          // 处理满减金额
          if (couponData.couponDiscountType === "FULL_REDUCTION") {
            form.conditionAmount = couponData.conditionAmount
              ? String(couponData.conditionAmount)
              : "";
            form.discountAmount = couponData.discountAmount
              ? String(couponData.discountAmount)
              : "";
            form.maxAmount = couponData.conditionAmount
              ? String(couponData.conditionAmount)
              : "";
            form.minAmount = couponData.discountAmount
              ? String(couponData.discountAmount)
              : "";
          }

          // 处理发放时间
          if (
            couponData.distributionStartTime &&
            couponData.distributionEndTime
          ) {
            // 确保时间戳是数字类型
            const startTime = Number(couponData.distributionStartTime);
            const endTime = Number(couponData.distributionEndTime);
            form.distributionTime = [
              formatTime(startTime, "YYYY-MM-DD"),
              formatTime(endTime, "YYYY-MM-DD")
            ];
            // 同步更新API字段
            form.distributionStartTime = startTime;
            form.distributionEndTime = endTime;
          } else {
            // 如果没有时间数据，设置为空数组
            form.distributionTime = [];
            form.distributionStartTime = null;
            form.distributionEndTime = null;
          }

          // 处理使用时间
          if (couponData.startTime && couponData.endTime) {
            // 确保时间戳是数字类型
            const startTime = Number(couponData.startTime);
            const endTime = Number(couponData.endTime);

            if (!isNaN(startTime) && !isNaN(endTime)) {
              form.useTimeType = 2; // 设置为有限
              form.useTimeRange = [
                formatTime(startTime, "YYYY-MM-DD"),
                formatTime(endTime, "YYYY-MM-DD")
              ];
              form.isUseLimit = true;
              // 同步更新API字段
              form.startTime = startTime;
              form.endTime = endTime;
            } else {
              console.warn(
                "🍪-----使用时间格式无效:",
                couponData.startTime,
                couponData.endTime
              );
              form.useTimeType = 1;
              form.isUseLimit = false;
              form.startTime = null;
              form.endTime = null;
              form.useTimeRange = [];
            }
          } else {
            form.useTimeType = 1; // 设置为不限
            form.isUseLimit = false;
            form.startTime = null;
            form.endTime = null;
            form.useTimeRange = [];
          }

          // 处理使用范围
          if (
            couponData.couponScope === "LIMIT" &&
            couponData.coursePeriods &&
            couponData.coursePeriods.length > 0
          ) {
            form.usedCourse = "LIMIT";
            form.couponScope = "LIMIT";
            // 从coursePeriods中提取ID
            form.coursePeriodIds = couponData.coursePeriods.map(
              item => item.id
            );

            // 如果是指定范围，需要加载课程列表并勾选已有项
            await loadCoursePeriodsAndSelectExisting(couponData.coursePeriods);
          }
        } catch (fieldError) {
          console.error("🍪-----字段回显错误:", fieldError);
          console.error("🍪-----问题字段数据:", couponData);
          ElMessage.warning("部分字段回显失败，请检查数据格式");
        }
      } else {
        ElMessage.error("获取优惠券数据失败");
      }
    } catch (error) {
    } finally {
      loading.value = false;
    }
  };

  // 页面初始化时加载数据
  const initializePage = async () => {
    // 检查是否是编辑模式（复制）
    if (route.query.type === "edit" && route.query.id) {
      await loadCouponData(route.query.id);
    } else {
      // 通用模式下不需要加载课程列表，因为所有课程都可以使用
    }
  };

  // 复选框配置
  const rowSelection = {
    type: "checkbox", // 复选框类型
    selectedRowKeys: selectedRowKeys, // 绑定选中的行 ID，不需要.value
    onChange: newSelectedRowKeys => {
      console.log("🍪-----复选框状态变化:", newSelectedRowKeys);
      // 选中状态变化时更新 selectedRowKeys
      selectedRowKeys.value = newSelectedRowKeys;

      // 根据选中的ID找到对应的行数据
      const selectedRowsData = tableData.value.filter(item =>
        newSelectedRowKeys.includes(item.id)
      );

      // 更新selectedRows
      selectedRows.value = selectedRowsData;

      // 更新rowList
      if (selectedRowsData.length > 0) {
        rowList.value = {
          coursePeriodId: selectedRowsData[0].id,
          couponAvailable: true,
          couponIds: newSelectedRowKeys
        };
      } else {
        rowList.value = {
          coursePeriodId: null,
          couponAvailable: null,
          couponIds: []
        };
      }

      console.log("🍪-----更新后的selectedRows:", selectedRows.value);
      console.log("🍪-----更新后的rowList:", rowList.value);
    }
  };

  // 页面加载完成后初始化
  onMounted(() => {
    try {
      initializePage();
    } catch (err) {
      console.error("🍪-----页面初始化失败:", err);
    }
  });

  return {
    // 路由相关
    router,
    route,

    // 表单相关
    form,
    formRef,
    rules,
    formData,
    formData2,
    // 使用范围相关
    scopeForm,
    searchColumns,
    tableColumns,
    tableData,
    stableTableData,
    showScopeSelection,

    // 分页相关
    pagination,

    // 状态相关
    loading,
    getListLoading,
    selectedRows,
    richFlag,
    formFile,

    // 表格引用
    // tableRef,
    rowSelection,

    // 方法
    onSearch,
    onReset,
    onPageChange,
    onSizeChange,
    onSelectionChange,
    onScopeChange,
    // onUseTimeTypeChange,
    reset,
    submitForm,
    onSubmit,

    // 其他数据
    newData,
    oldData
  };
}
