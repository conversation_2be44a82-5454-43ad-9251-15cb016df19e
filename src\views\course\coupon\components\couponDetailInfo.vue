<script setup>
import { ref, onMounted, computed } from "vue";
import { requestTo } from "@/utils/http/tool";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter } from "vue-router";
import { getCouponFindById } from "@/api/coupon.js";
import { COUPON_DISCOUNT_TYPE, COUPON_SCOPE } from "@/utils/enum.js";
import dayjs from "dayjs";
const props = defineProps({
  id: {
    type: Number,
    default: 0
  },
  // 查看使用范围是否显示
  showUseScope: {
    type: Boolean,
    default: true
  },
  valid: {
    type: String,
    default: "valid"
  }
});
const emit = defineEmits(["couponInfo"]);
const router = useRouter();
const tableHeader1 = ref([
  {
    id: "1",
    label: "优惠券名称：",
    value: "",
    prop: "name",
    width: "107px"
  },
  {
    id: "2",
    label: "优惠券类型：",
    value: "",
    prop: "couponDiscountType",
    width: "107px"
  },
  {
    id: "3",
    label: "使用范围：",
    value: "",
    prop: "couponScope",
    width: "107px"
  },
  {
    id: "4",
    label: "优惠门槛与金额：",
    value: "",
    prop: "conditionAmount",
    width: "107px"
  },
  {
    id: "5",
    label: "发放时间：",
    value: "",
    prop: "distributionStartTime",
    width: "107px"
  },

  {
    id: "6",
    label: "使用时间：",
    value: "",
    prop: "startTime",
    width: "107px"
  },
  {
    id: "7",
    label: "创建时间：",
    value: "",
    prop: "createdAt",
    width: "107px"
  },
  {
    id: "8",
    label: "备注：",
    value: "",
    prop: "remarks",
    width: "107px"
  }
]);
const tableHeaderValid = ref([
  {
    id: "1",
    label: "优惠券名称：",
    value: "",
    prop: "name",
    width: "107px"
  },
  {
    id: "2",
    label: "优惠券类型：",
    value: "",
    prop: "couponDiscountType",
    width: "107px"
  },
  {
    id: "3",
    label: "状态：",
    value: "",
    prop: "enabled",
    width: "107px"
  },
  {
    id: "4",
    label: "使用范围：",
    value: "",
    prop: "couponScope",
    width: "107px"
  },
  {
    id: "5",
    label: "优惠门槛与金额：",
    value: "",
    prop: "conditionAmount",
    width: "107px"
  },
  {
    id: "6",
    label: "发放时间：",
    value: "",
    prop: "distributionStartTime",
    width: "107px"
  },

  {
    id: "7",
    label: "使用时间：",
    value: "",
    prop: "startTime",
    width: "107px"
  },
  {
    id: "8",
    label: "创建时间：",
    value: "",
    prop: "createdAt",
    width: "107px"
  },
  {
    id: "9",
    label: "备注：",
    value: "",
    prop: "remarks",
    width: "107px"
  }
]);
const couponInfo = ref({});
const timeFn = (startTime, endTime) => {
  if (startTime && endTime) {
    return `${dayjs(startTime).format("YYYY-MM-DD")} 至 ${dayjs(endTime).format("YYYY-MM-DD")}`;
  } else if (startTime) {
    return `${dayjs(startTime).format("YYYY-MM-DD")}`;
  } else if (endTime) {
    return `${dayjs(endTime).format("YYYY-MM-DD")}`;
  } else {
    return "无限制";
  }
};
// 查询详情
const getData = async () => {
  let [err, res] = await requestTo(getCouponFindById({ id: props.id || 0 }));
  if (res) {
    console.log("🐬-----res1111--333---", res);
    couponInfo.value = res || {};
    emit("couponInfo", couponInfo.value);
    if (props.valid === "valid") {
      tableHeader.value[0].value = res.name || "--";
      tableHeader.value[1].value =
        COUPON_DISCOUNT_TYPE[res.couponDiscountType]?.label || "--";
      tableHeader.value[2].value = res.enabled === true ? "启用" : "停用";
      tableHeader.value[3].value = COUPON_SCOPE[res.couponScope]?.label || "--";
      if (res.couponDiscountType === "FULL_REDUCTION") {
        tableHeader.value[4].value = `满${res.conditionAmount || 0}减${res.discountAmount || 0}`;
      } else if (res.couponDiscountType === "DISCOUNT") {
        tableHeader.value[4].value = `${res.discountAmount || 0}折`;
      } else if (res.couponDiscountType === "FIXED") {
        tableHeader.value[4].value = `立减${res.discountAmount || 0}`;
      }

      tableHeader.value[5].value = timeFn(
        res.distributionStartTime,
        res.distributionEndTime
      );

      tableHeader.value[6].value = timeFn(res.startTime, res.endTime);

      tableHeader.value[7].value =
        dayjs(res.createdAt).format("YYYY-MM-DD HH:mm:ss") || "--";

      tableHeader.value[8].value = res.remarks || "--";
    } else {
      tableHeader.value[0].value = res.name || "--";
      tableHeader.value[1].value =
        COUPON_DISCOUNT_TYPE[res.couponDiscountType]?.label || "--";
      tableHeader.value[2].value = COUPON_SCOPE[res.couponScope]?.label || "--";
      if (res.couponDiscountType === "FULL_REDUCTION") {
        tableHeader.value[3].value = `满${res.conditionAmount || 0}减${res.discountAmount || 0}`;
      } else if (res.couponDiscountType === "DISCOUNT") {
        tableHeader.value[3].value = `${res.discountAmount || 0}折`;
      } else if (res.couponDiscountType === "FIXED") {
        tableHeader.value[3].value = `立减${res.discountAmount || 0}`;
      }

      tableHeader.value[4].value = timeFn(
        res.distributionStartTime,
        res.distributionEndTime
      );

      tableHeader.value[5].value = timeFn(res.startTime, res.endTime);

      tableHeader.value[6].value =
        dayjs(res.createdAt).format("YYYY-MM-DD HH:mm:ss") || "--";

      tableHeader.value[7].value = res.remarks || "--";
    }
  } else {
    console.log("🐳-----err-----", err);
  }
};
// 查看使用范围
const showUseScopeDetail = () => {
  router.push({
    path: "/course/coupon/detail/scopeDetails"
  });
};
const tableHeader = ref([]);
onMounted(() => {
  if (props.valid === "valid") {
    tableHeader.value = tableHeaderValid.value;
  } else {
    tableHeader.value = tableHeader1.value;
  }
  getData();
});
</script>

<template>
  <div>
    <div v-if="showUseScope" class="margin-top-btn">
      <el-button type="primary" @click="showUseScopeDetail">
        查看使用范围
      </el-button>
    </div>
    <el-descriptions
      class="margin-top"
      title=""
      :column="2"
      border
      style="width: 100%"
      :label-width="'200px'"
    >
      <template v-for="(item, index) in tableHeader" :key="index">
        <el-descriptions-item
          label-class-name="my-label"
          label-align="right"
          :span="item.span || 1"
        >
          <template #label>
            <div class="cell-item">
              {{ item.label }}
            </div>
          </template>
          <div>
            <div>{{ item.value || "--" }}</div>
          </div>
        </el-descriptions-item>
      </template>
    </el-descriptions>
  </div>
</template>

<style lang="scss" scoped>
:deep(.my-label) {
  background: #fff !important;
}
:deep(
  .el-descriptions__body
    .el-descriptions__table.is-bordered
    .el-descriptions__cell
) {
  border: 0px !important;
}
:deep(.el-descriptions__label.el-descriptions__cell.is-bordered-label) {
  font-weight: normal;
}

.margin-top-btn {
  display: flex;
  justify-content: right;
}
</style>
