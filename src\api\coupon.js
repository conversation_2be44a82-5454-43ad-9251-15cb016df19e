import { http } from "@/utils/http";

// 新增优惠券
export const addCoupon = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/coupon/save",
    { data },
    {
      isNeedEncrypt: true,
      isNeedToken: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COUPON_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};

// 查询可使用优惠券课期
export const getCouponCourse = params => {
  return http.request(
    "get",
    "/organization/coursePeriod/findCouponAvailable",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
// 查询优惠券列表
export const getCouponFindAll = params => {
  return http.request(
    "get",
    "/organization/coupon/findAll",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
// 查询优惠券详情
export const getCouponFindById = params => {
  return http.request(
    "get",
    "/organization/coupon/findById",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
// 停用使用优惠券
export const enabledCoupon = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/coupon/enabled",
    { data },
    {
      isNeedEncrypt: true,
      isNeedToken: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COUPON_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
// 手动派发优惠券
export const distributeCoupon = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/coupon/distributeCoupon",
    { data },
    {
      isNeedEncrypt: true,
      isNeedToken: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COUPON_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
//查询优惠券领取记录
export const findReceiveRecordByCouponId = params => {
  return http.request(
    "get",
    "/organization/coupon/findReceiveRecordByCouponId",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
// 根据课期Id查询优惠券-分页
export const findCouponPageByCoursePeriodId = params => {
  return http.request(
    "get",
    "/organization/coupon/findCouponPageByCoursePeriodId",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};

// 根据课期Id查询优惠券-不分页
export const findCouponByCoursePeriodId = params => {
  return http.request(
    "get",
    "/organization/coupon/findCouponByCoursePeriodId",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};

// 创建或编辑课期优惠券
export const updateCoursePeriodCoupon = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/priceSetting/updateCoursePeriodCoupon",
    { data },
    {
      isNeedEncrypt: true,
      isNeedToken: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};

// 查询可使用优惠券课期
export const getCouponCourseList = params => {
  return http.request(
    "get",
    "/organization/coursePeriod/findCouponAvailable",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};

// 编辑课期优惠券
export const createOrUpdateCoupon = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/priceSetting/updateCoursePeriodCoupon",
    { data },
    {
      isNeedEncrypt: true,
      isNeedToken: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COUPON_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
